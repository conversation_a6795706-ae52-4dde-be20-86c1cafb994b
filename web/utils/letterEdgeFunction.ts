import { createClient } from '@/lib/supabase';
import { ResumeInput, JobInput } from '@/types/resume';
import { StructuredLetterData } from '@/types/letter-structured';

export interface LetterEdgeFunctionRequest {
  letterId: string;
  resumeInput: ResumeInput;
  jobInput: JobInput;
  templateId?: string;
}

export interface LetterEdgeFunctionResponse {
  success: boolean;
  letterId?: string;
  structuredData?: StructuredLetterData;
  message?: string;
  error?: string;
}

export interface LetterGenerationStatus {
  id: string;
  user_id: string;
  data: {
    resumeInput?: any;
    jobInput?: any;
    templateId?: string;
    error_message?: string;
    generatedAt?: string;
  };
  structured_data?: StructuredLetterData;
  html?: string;
  status: 'processing' | 'done' | 'error';
  pdf_url?: string;
  created_at: string;
  updated_at: string;
  error_message?: string;
}

/**
 * Converts ArrayBuffer to base64 string
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  
  for (let i = 0; i < bytes.length; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  
  return btoa(binary);
}

/**
 * Converts ResumeInput and JobInput with ArrayBuffers to base64 format for Edge Function
 */
function serializeRequestForLetterEdgeFunction(request: LetterEdgeFunctionRequest): any {
  // Create a deep copy manually to handle ArrayBuffers properly
  const serializedRequest: any = {
    letterId: request.letterId,
    resumeInput: {},
    jobInput: {},
    templateId: request.templateId
  };
  
  // Handle resume input
  if (request.resumeInput.file) {
    serializedRequest.resumeInput.file = {
      mimeType: request.resumeInput.file.mimeType
    };

    console.log('buffer exists', request.resumeInput.file.buffer);

    console.log('buffer is array buffer', request.resumeInput.file.buffer instanceof ArrayBuffer);

    console.log('buffer is undefined', request.resumeInput.file.buffer === undefined);

    console.log('buffer is null', request.resumeInput.file.buffer === null);

    console.log('buffer type', typeof request.resumeInput.file.buffer);
    
    // Convert ArrayBuffer to base64 if present
    if (request.resumeInput.file.buffer && request.resumeInput.file.buffer instanceof ArrayBuffer) {
      serializedRequest.resumeInput.file.buffer = arrayBufferToBase64(request.resumeInput.file.buffer);
      console.log('buffer converted')
    }
    
    // Copy extractedText if present
    if (request.resumeInput.file.extractedText) {
      serializedRequest.resumeInput.file.extractedText = request.resumeInput.file.extractedText;
      console.log('buffer copied')
    }
  }
  
  if (request.resumeInput.manual) {
    serializedRequest.resumeInput.manual = { ...request.resumeInput.manual };
  }
  
  // Handle job input
  if (request.jobInput.description) {
    serializedRequest.jobInput.description = request.jobInput.description;
  }
  
  if (request.jobInput.image) {
    serializedRequest.jobInput.image = {
      mimeType: request.jobInput.image.mimeType
    };
    
    // Convert ArrayBuffer to base64 if present
    if (request.jobInput.image.buffer && request.jobInput.image.buffer instanceof ArrayBuffer) {
      serializedRequest.jobInput.image.buffer = arrayBufferToBase64(request.jobInput.image.buffer);
    }
  }
  
  return serializedRequest;
}

/**
 * Calls the generate-letter Edge Function from the frontend with authentication
 */
export async function callGenerateLetterEdgeFunctionFromFrontend(
  request: LetterEdgeFunctionRequest
): Promise<LetterEdgeFunctionResponse> {
  console.log('=== Frontend Letter Edge Function Call START ===');
  console.log('1. Request Summary:', {
    letterId: request.letterId,
    resumeInputType: request.resumeInput?.file ? 'file' : request.resumeInput?.manual ? 'manual' : 'none',
    jobInputType: request.jobInput?.description ? 'text' : request.jobInput?.image ? 'image' : 'none',
    resumeFileType: request.resumeInput?.file?.mimeType,
    resumeFileBuffer: request.resumeInput?.file?.buffer,
    jobImageType: request.jobInput?.image?.mimeType,
    jobImageBuffer: request.jobInput?.image?.buffer,
    templateId: request.templateId
  });

  // Environment validation
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseAnonKey) {
    const missingVars = [];
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseAnonKey) missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  const supabase = createClient();
  
  // Get the user session for authentication
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  
  if (sessionError || !session) {
    console.error('Authentication error:', sessionError);
    throw new Error('User not authenticated');
  }

  try {
    console.log('2. Invoking Edge Function "generate-letter" from frontend...');
    
    // Serialize the request to convert ArrayBuffers to base64
    const serializedRequest = serializeRequestForLetterEdgeFunction(request);

    console.log('request data: ', serializedRequest)
    
    const requestBodyString = JSON.stringify(serializedRequest);
    console.log('3. Request Body String Length:', requestBodyString.length);
    
    // Call the generate-letter edge function with authentication
    const { data, error } = await supabase.functions.invoke('generate-letter', {
      body: serializedRequest,
      headers: {
        Authorization: `Bearer ${session.access_token}`
      }
    });

    console.log('4. Edge Function Response Received:', {
      hasData: !!data,
      dataSuccess: data?.success,
      hasError: !!error
    });

    if (error) {
      console.error('5. Edge Function Error Details:', error);
      // Update the letter status to error in case edge function failed to do so
      try {
        await supabase
          .from('letters')
          .update({
            status: 'error',
            error_message: error.message || 'Edge function call failed',
            updated_at: new Date().toISOString()
          })
          .eq('id', request.letterId);
      } catch (updateError) {
        console.error('Failed to update letter status to error:', updateError);
      }
      
      throw new Error(`Edge Function Error: ${error.message || 'Unknown error'}`);
    }

    if (!data) {
      console.error('Edge Function returned no data');
      // Update the letter status to error
      try {
        await supabase
          .from('letters')
          .update({
            status: 'error',
            error_message: 'Edge function returned no data',
            updated_at: new Date().toISOString()
          })
          .eq('id', request.letterId);
      } catch (updateError) {
        console.error('Failed to update letter status to error:', updateError);
      }
      
      throw new Error('Edge Function returned no data');
    }

    if (!data.success) {
      console.error('Edge Function returned error:', data.error);
      // The edge function should have already updated the letter status, but let's make sure
      throw new Error(data.error || 'Edge function reported failure');
    }

    console.log('6. Edge Function Success:', { letterId: data.letterId, hasStructuredData: !!data.structuredData });
    console.log('=== Frontend Letter Edge Function Call END (SUCCESS) ===');
    
    return data as LetterEdgeFunctionResponse;
  } catch (error) {
    console.error('7. Edge Function Call Exception:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error)
    });
    console.log('=== Frontend Letter Edge Function Call END (ERROR) ===');
    
    // Update letter status to error if not already done
    try {
      await supabase
        .from('letters')
        .update({
          status: 'error',
          error_message: error instanceof Error ? error.message : String(error),
          updated_at: new Date().toISOString()
        })
        .eq('id', request.letterId);
    } catch (updateError) {
      console.error('Failed to update letter status to error:', updateError);
    }
    
    throw error;
  }
}

/**
 * Calls the generate-letter Edge Function (legacy - for server-side use)
 */
export async function callGenerateLetterEdgeFunction(
  request: LetterEdgeFunctionRequest
): Promise<void> {
  console.log('=== Letter Edge Function Diagnostic START ===');
  console.log('1. Request Summary:', {
    letterId: request.letterId,
    resumeInputType: request.resumeInput.file ? 'file' : 'manual',
    jobInputType: request.jobInput.description ? 'text' : 'image',
    resumeFileType: request.resumeInput.file?.mimeType,
    jobImageType: request.jobInput.image?.mimeType,
    templateId: request.templateId
  });
  
  console.log('1.5. Original Request Detailed Check:', {
    requestKeys: Object.keys(request),
    resumeInputKeys: Object.keys(request.resumeInput),
    jobInputKeys: Object.keys(request.jobInput),
    resumeFileBuffer: request.resumeInput.file?.buffer ? `ArrayBuffer(${request.resumeInput.file.buffer.byteLength} bytes)` : 'NONE',
    resumeFileExtractedText: request.resumeInput.file?.extractedText ? `Text(${request.resumeInput.file.extractedText.length} chars)` : 'NONE',
    jobImageBuffer: request.jobInput.image?.buffer ? `ArrayBuffer(${request.jobInput.image.buffer.byteLength} bytes)` : 'NONE',
    jobDescription: request.jobInput.description ? `Text(${request.jobInput.description.length} chars)` : 'NONE'
  });

  // Environment validation
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  console.log('2. Environment Check:', {
    supabaseUrl: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'MISSING',
    supabaseAnonKey: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'MISSING',
    edgeFunctionUrl: supabaseUrl ? `${supabaseUrl}/functions/v1/generate-letter` : 'UNDEFINED'
  });

  if (!supabaseUrl || !supabaseAnonKey) {
    const missingVars = [];
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseAnonKey) missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  const supabase = createClient();
  console.log('3. Supabase client created');
  
  try {
    console.log('4. Invoking Edge Function "generate-letter"...');
    
    // Serialize the request to convert ArrayBuffers to base64
    const serializedRequest = serializeRequestForLetterEdgeFunction(request);
    
    console.log('5. Serialized Request Debug:', {
      requestKeys: Object.keys(serializedRequest),
      resumeInputKeys: Object.keys(serializedRequest.resumeInput || {}),
      jobInputKeys: Object.keys(serializedRequest.jobInput || {}),
      letterId: serializedRequest.letterId,
      hasResumeFile: !!serializedRequest.resumeInput?.file,
      hasResumeManual: !!serializedRequest.resumeInput?.manual,
      hasJobDescription: !!serializedRequest.jobInput?.description,
      hasJobImage: !!serializedRequest.jobInput?.image,
      templateId: serializedRequest.templateId
    });
    
    const requestBodyString = JSON.stringify(serializedRequest);
    console.log('6. Request Body String Length:', requestBodyString.length);
    console.log('7. Request Body Preview (first 500 chars):', requestBodyString.substring(0, 500));
    
    // Call the generate-letter edge function
    const { data, error } = await supabase.functions.invoke('generate-letter', {
      body: serializedRequest,
    });

    console.log('8. Edge Function Response Received:', {
      hasData: !!data,
      dataType: typeof data,
      hasError: !!error,
      errorType: typeof error
    });

    if (error) {
      console.error('9. Edge Function Error Details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        context: error.context
      });
      
      throw new Error(`Edge Function Error: ${error.message || 'Unknown error'}${error.details ? ` (${error.details})` : ''}${error.hint ? ` Hint: ${error.hint}` : ''}`);
    }

    if (!data) {
      throw new Error('Edge Function returned no data');
    }

    console.log('10. Edge Function Success:', data);
    console.log('=== Letter Edge Function Diagnostic END (SUCCESS) ===');
  } catch (error) {
    console.error('11. Edge Function Call Exception:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    console.log('=== Letter Edge Function Diagnostic END (ERROR) ===');
    
    // Re-throw the error to match the expected behavior
    throw error;
  }
}

/**
 * Creates a new letter generation request in the database
 */
export async function createLetterGenerationRequest(
  userId: string,
  resumeInput: ResumeInput,
  jobInput: JobInput,
  templateId?: string
): Promise<string> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('letters')
    .insert({
      user_id: userId,
      data: { resumeInput, jobInput, templateId },
      status: 'processing'
    })
    .select('id')
    .single();

  if (error) {
    throw new Error(`Failed to create letter generation request: ${error.message}`);
  }

  return data.id;
}

/**
 * Gets the status of a letter generation request
 */
export async function getLetterGenerationStatus(
  letterId: string
): Promise<LetterGenerationStatus | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('letters')
    .select('id, user_id, data, structured_data, html, status, pdf_url, created_at, updated_at, error_message')
    .eq('id', letterId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Record not found
    }
    throw new Error(`Failed to get letter generation status: ${error.message}`);
  }

  return data as LetterGenerationStatus;
}

/**
 * Polls for letter generation completion
 */
export async function pollLetterGenerationStatus(
  letterId: string,
  maxAttempts: number = 60, // 60 attempts = 5 minutes with 5-second intervals
  intervalMs: number = 5000
): Promise<LetterGenerationStatus> {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    
    const pollInterval = setInterval(async () => {
      attempts++;
      
      try {
        const status = await getLetterGenerationStatus(letterId);
        
        if (!status) {
          clearInterval(pollInterval);
          reject(new Error('Letter generation record not found'));
          return;
        }

        // Check if generation is complete
        if (status.status === 'done' || status.status === 'error') {
          clearInterval(pollInterval);
          resolve(status);
          return;
        }

        // Check if we've exceeded max attempts
        if (attempts >= maxAttempts) {
          clearInterval(pollInterval);
          reject(new Error('Letter generation timed out'));
          return;
        }
      } catch (error) {
        clearInterval(pollInterval);
        reject(error);
      }
    }, intervalMs);
  });
}

/**
 * Initiates async letter generation and returns immediately
 */
export async function initiateAsyncLetterGeneration(
  userId: string,
  resumeInput: ResumeInput,
  jobInput: JobInput,
  templateId?: string
): Promise<string> {
  // Create the database record
  const letterId = await createLetterGenerationRequest(
    userId,
    resumeInput,
    jobInput,
    templateId
  );

  // Call the Edge Function (fire and forget)
  const request: LetterEdgeFunctionRequest = {
    letterId,
    resumeInput,
    jobInput,
    templateId
  };

  // Don't await this - let it run in the background
  callGenerateLetterEdgeFunction(request).catch(error => {
    console.error('Letter Edge Function call failed:', error);
  });

  return letterId;
}

/**
 * Initiates async letter generation and waits for completion
 */
export async function generateLetterAndWait(
  userId: string,
  resumeInput: ResumeInput,
  jobInput: JobInput,
  templateId?: string,
  maxWaitTimeMs: number = 300000 // 5 minutes
): Promise<LetterGenerationStatus> {
  // Initiate the generation
  const letterId = await initiateAsyncLetterGeneration(
    userId,
    resumeInput,
    jobInput,
    templateId
  );

  // Wait for completion
  const maxAttempts = Math.floor(maxWaitTimeMs / 5000);
  return await pollLetterGenerationStatus(letterId, maxAttempts);
}

/**
 * Gets all letter generation requests for a user
 */
export async function getUserLetterGenerations(
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<LetterGenerationStatus[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('letters')
    .select('id, user_id, data, structured_data, html, status, pdf_url, created_at, updated_at, error_message')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw new Error(`Failed to get user letter generations: ${error.message}`);
  }

  return data as LetterGenerationStatus[];
}

/**
 * Deletes a letter generation request
 */
export async function deleteLetterGeneration(letterId: string): Promise<void> {
  const supabase = createClient();
  
  const { error } = await supabase
    .from('letters')
    .delete()
    .eq('id', letterId);

  if (error) {
    throw new Error(`Failed to delete letter generation: ${error.message}`);
  }
}

/**
 * Updates a letter generation request
 */
export async function updateLetterGeneration(
  letterId: string,
  updates: Partial<LetterGenerationStatus>
): Promise<void> {
  const supabase = createClient();
  
  const { error } = await supabase
    .from('letters')
    .update(updates)
    .eq('id', letterId);

  if (error) {
    throw new Error(`Failed to update letter generation: ${error.message}`);
  }
}